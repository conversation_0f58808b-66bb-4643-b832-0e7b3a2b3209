<!--
/**
 * Customer Insider Rewards Template
 * Displays customer's available insider rewards points in the logged user section
 */
-->
<div class="customer-insider-rewards" data-bind="visible: isVisible()">
    <div class="insider-rewards-wrapper">
        <div class="insider-rewards-icon-wrapper">
            <span class="insider-rewards-icon gold-icon"></span>
            <span class="insider-level-text">GOLD</span>
        </div>
        <div class="insider-rewards-content">
            <span class="insider-rewards-title">Insider Rewards</span>
            <span class="insider-rewards-amount" data-bind="text: getAvailablePoints()"></span>
            <span class="insider-rewards-label">Available</span>
        </div>
    </div>
</div>
<!-- Debug info -->
<div style="background: red; color: white; padding: 10px; margin: 10px 0;">
    DEBUG: Customer Insider Rewards Component Loaded
    <br>Customer Logged In: <span data-bind="text: isCustomerLoggedIn()"></span>
    <br>Is Visible: <span data-bind="text: isVisible()"></span>
    <br>Available Points: <span data-bind="text: getAvailablePoints()"></span>
</div>
