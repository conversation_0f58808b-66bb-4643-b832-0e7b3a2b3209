define([
    'ko',
    'uiComponent',
    'Magento_Customer/js/model/customer'
], function (ko, Component, customer) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Magento_Checkout/customer-insider-rewards'
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            console.log('Customer Insider Rewards component initialized');
            console.log('Customer logged in:', customer.isLoggedIn());
            console.log('Customer object:', customer);

            // Force visibility for debugging
            window.customerInsiderRewardsDebug = this;

            return this;
        },

        /**
         * Check if customer is logged in
         */
        isCustomerLoggedIn: function () {
            return customer.isLoggedIn();
        },

        /**
         * Check if component should be visible
         */
        isVisible: function () {
            var isLoggedIn = this.isCustomerLoggedIn();
            console.log('Customer insider rewards visible:', isLoggedIn);
            return isLoggedIn;
        },

        /**
         * Get customer's available insider points
         */
        getAvailablePoints: function () {
            if (!this.isCustomerLoggedIn()) {
                return '$0.00';
            }

            // For now, return static value - you can connect this to your InsiderRewards block later
            return '$152.00';
        }
    });
});
