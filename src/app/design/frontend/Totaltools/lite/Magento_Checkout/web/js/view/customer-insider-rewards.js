define([
    'ko',
    'uiComponent',
    'Magento_Customer/js/model/customer',
], function (ko, Component, customer) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Magento_Checkout/customer-insider-rewards'
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            this.isCustomerLoggedIn= customer.isLoggedIn();
            return this;
        },

      
        isCustomerLoggedIn: function () {
            return isCustomerLoggedIn;
        },

      
        isVisible: function () {
            return this.isCustomerLoggedIn();
        },

       
        getAvailablePoints: function () {
            if (!this.isCustomerLoggedIn()) {
                return '$0.00';
            }
            
            return '$102.00';
        }
    });
});
