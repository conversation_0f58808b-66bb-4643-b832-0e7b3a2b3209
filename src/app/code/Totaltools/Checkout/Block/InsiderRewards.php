<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 */

namespace Totaltools\Checkout\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Class InsiderRewards
 * Block class for handling insider rewards data in checkout
 */
class InsiderRewards extends Template
{
    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var CheckoutSession
     */
    protected $checkoutSession;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * InsiderRewards constructor.
     *
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param CheckoutSession $checkoutSession
     * @param StoreManagerInterface $storeManager
     * @param array $data
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        CheckoutSession $checkoutSession,
        StoreManagerInterface $storeManager,
        array $data = []
    ) {
        $this->customerSession = $customerSession;
        $this->checkoutSession = $checkoutSession;
        $this->storeManager = $storeManager;
        parent::__construct($context, $data);
    }

    /**
     * Check if customer is logged in
     *
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->customerSession->isLoggedIn();
    }

    /**
     * Get customer's current insider rewards points
     * For now, returns a static value as requested
     *
     * @return string
     */
    public function getCustomerInsiderPoints()
    {
        if (!$this->isCustomerLoggedIn()) {
            return '0';
        }

        // TODO: Replace with actual API call to get customer's insider points
        // For now, returning static value as requested
        return '$152.00';
    }

    /**
     * Get insider points to be earned from current order
     * For now, returns a static value as requested
     *
     * @return string
     */
    public function getPointsToEarn()
    {
        // TODO: Replace with actual calculation based on order total
        // For now, returning static value as requested
        return '563';
    }

    /**
     * Get current quote total
     *
     * @return float
     */
    public function getQuoteTotal()
    {
        try {
            $quote = $this->checkoutSession->getQuote();
            return $quote ? $quote->getGrandTotal() : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Format price with currency symbol
     *
     * @param float $price
     * @return string
     */
    public function formatPrice($price)
    {
        return $this->_storeManager->getStore()->getCurrentCurrency()->format($price, [], false);
    }
}
