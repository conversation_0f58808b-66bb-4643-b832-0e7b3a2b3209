<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 */

namespace Totaltools\Checkout\Controller\Insider;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Totaltools\Checkout\Block\InsiderRewards;

/**
 * Class Points
 * Controller for getting customer insider rewards points via AJAX
 */
class Points implements HttpGetActionInterface
{
    /**
     * @var JsonFactory
     */
    protected $jsonFactory;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var InsiderRewards
     */
    protected $insiderRewardsBlock;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * Points constructor.
     *
     * @param JsonFactory $jsonFactory
     * @param CustomerSession $customerSession
     * @param InsiderRewards $insiderRewardsBlock
     * @param RequestInterface $request
     */
    public function __construct(
        JsonFactory $jsonFactory,
        CustomerSession $customerSession,
        InsiderRewards $insiderRewardsBlock,
        RequestInterface $request
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->customerSession = $customerSession;
        $this->insiderRewardsBlock = $insiderRewardsBlock;
        $this->request = $request;
    }

    /**
     * Execute action
     *
     * @return ResponseInterface|ResultInterface
     */
    public function execute()
    {
        $result = $this->jsonFactory->create();

        try {
            // Check if customer is logged in
            if (!$this->customerSession->isLoggedIn()) {
                return $result->setData([
                    'success' => false,
                    'message' => 'Customer not logged in',
                    'points' => '$0.00'
                ]);
            }

            // Get customer points from the block
            $points = $this->insiderRewardsBlock->getCustomerInsiderPoints();

            return $result->setData([
                'success' => true,
                'points' => $points,
                'message' => 'Points retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return $result->setData([
                'success' => false,
                'message' => 'Error retrieving points: ' . $e->getMessage(),
                'points' => '$0.00'
            ]);
        }
    }
}
